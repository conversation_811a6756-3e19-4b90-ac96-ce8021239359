#!/usr/bin/env python3
"""
GPU Processing Validation Test for WOSS Seismic Analysis Tool

This script validates GPU processing capabilities, memory management,
and performance characteristics for all analysis modes.
"""

import sys
import os
import logging
import numpy as np
import time
from pathlib import Path

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_gpu_system_initialization():
    """Test GPU system initialization and fallback mechanisms."""
    print("Testing GPU system initialization...")
    
    try:
        from utils.gpu_utils import initialize_gpu_system, GPU_AVAILABLE, GPU_DEVICE_INFO
        
        # Test initialization
        gpu_available, backend, device_info = initialize_gpu_system()
        
        print(f"✅ GPU Available: {gpu_available}")
        print(f"✅ Backend: {backend}")
        print(f"✅ Device Info: {device_info}")
        
        # Test global variables
        print(f"✅ Global GPU_AVAILABLE: {GPU_AVAILABLE}")
        print(f"✅ Global GPU_DEVICE_INFO: {GPU_DEVICE_INFO}")
        
        return True
        
    except Exception as e:
        print(f"❌ GPU initialization error: {e}")
        return False

def test_processing_backend_selection():
    """Test processing backend selection and function availability."""
    print("\nTesting processing backend selection...")
    
    try:
        from utils.gpu_utils import get_processing_functions, get_processing_backend
        
        # Test backend selection
        backend_type, descriptor_func = get_processing_backend()
        print(f"✅ Selected backend: {backend_type}")
        print(f"✅ Descriptor function available: {descriptor_func is not None}")
        
        # Test processing functions
        processing_funcs = get_processing_functions()
        print(f"✅ Processing functions backend: {processing_funcs['backend']}")
        print(f"✅ Single trace function: {processing_funcs['single'] is not None}")
        print(f"✅ Batch function: {processing_funcs['batch'] is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Backend selection error: {e}")
        return False

def test_batch_size_optimization():
    """Test batch size optimization for different analysis modes."""
    print("\nTesting batch size optimization...")
    
    try:
        from utils.gpu_utils import get_optimal_processing_config
        from common.constants import ANALYSIS_MODES
        
        test_trace_counts = [100, 1000, 10000, 50000]
        
        for mode in ANALYSIS_MODES:
            print(f"\n📊 Testing mode: {mode}")
            
            for trace_count in test_trace_counts:
                config = get_optimal_processing_config(mode, trace_count)
                
                print(f"  Traces: {trace_count:>6} | Backend: {config['backend']:>3} | "
                      f"Batch: {config['batch_size']:>4} | Batches: {config['estimated_batches']:>3}")
                
                # Validate configuration
                assert config['backend'] in ['GPU', 'CPU'], f"Invalid backend: {config['backend']}"
                assert config['batch_size'] > 0, f"Invalid batch size: {config['batch_size']}"
                assert config['estimated_batches'] > 0, f"Invalid batch count: {config['estimated_batches']}"
        
        print("✅ Batch size optimization working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Batch size optimization error: {e}")
        return False

def test_memory_management():
    """Test GPU memory management functions."""
    print("\nTesting memory management...")
    
    try:
        from utils.gpu_utils import clear_gpu_memory, GPU_AVAILABLE
        
        # Test memory clearing (should work even without GPU)
        clear_gpu_memory()
        print("✅ Memory clearing function executed successfully")
        
        if not GPU_AVAILABLE:
            print("⚠️ GPU not available - testing CPU memory management")
            
            # Test CPU memory management
            import gc
            gc.collect()
            print("✅ CPU garbage collection executed")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory management error: {e}")
        return False

def test_processing_modes():
    """Test GPU processing functions for all analysis modes."""
    print("\nTesting processing mode functions...")
    
    try:
        from utils.processing_gpu import (
            process_inline_analysis_gpu,
            process_crossline_analysis_gpu,
            process_aoi_analysis_gpu,
            process_polyline_analysis_gpu,
            process_traces_gpu_batch
        )
        
        print("✅ All processing mode functions imported successfully")
        
        # Test function signatures (without actual execution)
        functions_to_test = [
            ('process_inline_analysis_gpu', process_inline_analysis_gpu),
            ('process_crossline_analysis_gpu', process_crossline_analysis_gpu),
            ('process_aoi_analysis_gpu', process_aoi_analysis_gpu),
            ('process_polyline_analysis_gpu', process_polyline_analysis_gpu),
            ('process_traces_gpu_batch', process_traces_gpu_batch)
        ]
        
        for func_name, func in functions_to_test:
            if callable(func):
                print(f"✅ {func_name} is callable")
            else:
                print(f"❌ {func_name} is not callable")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Processing modes error: {e}")
        return False

def test_performance_characteristics():
    """Test performance characteristics and benchmarking."""
    print("\nTesting performance characteristics...")
    
    try:
        from utils.gpu_utils import get_optimal_processing_config
        
        # Simulate performance testing with different data sizes
        data_sizes = [
            ("Small dataset", 1000),
            ("Medium dataset", 10000),
            ("Large dataset", 100000),
            ("Very large dataset", 500000)
        ]
        
        print("\n📈 Performance Characteristics:")
        print("Dataset Size          | Backend | Batch Size | Est. Batches | Est. Time")
        print("-" * 70)
        
        for size_name, trace_count in data_sizes:
            config = get_optimal_processing_config("By inline/crossline section (AOI)", trace_count)
            
            # Estimate processing time (rough calculation)
            time_per_batch = 0.1 if config['backend'] == 'GPU' else 0.5  # seconds
            estimated_time = config['estimated_batches'] * time_per_batch
            
            print(f"{size_name:<20} | {config['backend']:>7} | {config['batch_size']:>10} | "
                  f"{config['estimated_batches']:>12} | {estimated_time:>8.1f}s")
        
        print("✅ Performance characteristics calculated successfully")
        return True
        
    except Exception as e:
        print(f"❌ Performance characteristics error: {e}")
        return False

def test_cpu_fallback_mechanisms():
    """Test CPU fallback mechanisms when GPU is not available."""
    print("\nTesting CPU fallback mechanisms...")

    try:
        from utils.gpu_utils import GPU_AVAILABLE

        if not GPU_AVAILABLE:
            print("✅ CPU fallback mode active (expected)")

            # Test that CPU processing functions are available
            from utils.processing_gpu import process_traces_gpu_batch
            from utils.gpu_utils import get_processing_functions

            processing_funcs = get_processing_functions()

            if processing_funcs['backend'] == 'CPU':
                print("✅ CPU processing functions loaded")
            else:
                print(f"❌ Expected CPU backend, got: {processing_funcs['backend']}")
                return False

            # Test CPU batch size optimization
            from utils.gpu_utils import get_optimal_processing_config
            config = get_optimal_processing_config("Single inline (all crosslines)", 1000)

            if config['backend'] == 'CPU':
                print(f"✅ CPU batch size optimization: {config['batch_size']}")
            else:
                print(f"❌ Expected CPU backend in config, got: {config['backend']}")
                return False
        else:
            print("⚠️ GPU available - cannot test CPU fallback")

        return True

    except Exception as e:
        print(f"❌ CPU fallback mechanisms error: {e}")
        return False

def test_processing_simulation():
    """Test actual processing simulation with mock data."""
    print("\nTesting processing simulation...")

    try:
        from utils.gpu_utils import get_optimal_processing_config, get_processing_functions
        import numpy as np

        # Create mock trace data
        num_traces = 100
        samples_per_trace = 1000
        mock_traces = np.random.randn(num_traces, samples_per_trace).astype(np.float32)

        print(f"✅ Created mock data: {num_traces} traces x {samples_per_trace} samples")

        # Test processing configuration
        config = get_optimal_processing_config("By inline/crossline section (AOI)", num_traces)
        print(f"✅ Processing config: {config['backend']} backend, batch size {config['batch_size']}")

        # Test processing functions availability
        processing_funcs = get_processing_functions()
        if processing_funcs['single'] is not None:
            print("✅ Single trace processing function available")

        # Simulate batch processing timing
        batch_size = config['batch_size']
        num_batches = config['estimated_batches']

        start_time = time.time()

        # Simulate processing batches
        for batch_idx in range(min(num_batches, 3)):  # Test first 3 batches only
            batch_start = batch_idx * batch_size
            batch_end = min(batch_start + batch_size, num_traces)
            batch_traces = mock_traces[batch_start:batch_end]

            # Simulate processing time
            time.sleep(0.01)  # Small delay to simulate processing

            print(f"  Batch {batch_idx + 1}: processed {len(batch_traces)} traces")

        elapsed_time = time.time() - start_time
        print(f"✅ Simulated processing completed in {elapsed_time:.3f} seconds")

        return True

    except Exception as e:
        print(f"❌ Processing simulation error: {e}")
        return False

def test_memory_usage_estimation():
    """Test memory usage estimation for different dataset sizes."""
    print("\nTesting memory usage estimation...")

    try:
        import psutil

        # Get current memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / (1024 * 1024)  # MB
        print(f"✅ Initial memory usage: {initial_memory:.1f} MB")

        # Test memory estimation for different data sizes
        test_cases = [
            ("Small survey", 1000, 1000),
            ("Medium survey", 10000, 1500),
            ("Large survey", 100000, 2000),
            ("Very large survey", 500000, 2500)
        ]

        print("\n📊 Memory Usage Estimation:")
        print("Dataset               | Traces  | Samples | Est. Memory | Batch Size")
        print("-" * 70)

        for name, traces, samples in test_cases:
            # Estimate memory usage
            bytes_per_sample = 4  # float32
            base_memory = traces * samples * bytes_per_sample
            processing_overhead = 2.5
            estimated_mb = (base_memory * processing_overhead) / (1024 * 1024)

            # Get optimal batch size
            from utils.gpu_utils import get_optimal_processing_config
            config = get_optimal_processing_config("By inline/crossline section (AOI)", traces)

            print(f"{name:<20} | {traces:>7} | {samples:>7} | {estimated_mb:>10.1f} MB | {config['batch_size']:>10}")

        print("✅ Memory usage estimation completed")
        return True

    except Exception as e:
        print(f"❌ Memory usage estimation error: {e}")
        return False

def main():
    """Run all GPU processing validation tests."""
    print("=" * 70)
    print("WOSS Seismic Analysis Tool - GPU Processing Validation")
    print("=" * 70)
    
    tests = [
        ("GPU System Initialization", test_gpu_system_initialization),
        ("Processing Backend Selection", test_processing_backend_selection),
        ("Batch Size Optimization", test_batch_size_optimization),
        ("Memory Management", test_memory_management),
        ("Processing Modes", test_processing_modes),
        ("Performance Characteristics", test_performance_characteristics),
        ("CPU Fallback Mechanisms", test_cpu_fallback_mechanisms),
        ("Processing Simulation", test_processing_simulation),
        ("Memory Usage Estimation", test_memory_usage_estimation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"GPU Processing Validation Results: {passed}/{total} tests passed")
    print("=" * 70)
    
    if passed == total:
        print("🎉 All GPU processing validation tests PASSED!")
        return 0
    else:
        print("⚠️ Some GPU processing validation tests FAILED.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
