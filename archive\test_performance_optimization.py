#!/usr/bin/env python3
"""
Performance Optimization Test for WOSS Seismic Analysis Tool

This script tests performance optimizations, user experience improvements,
and deployment readiness for the modular WOSS application.
"""

import sys
import os
import logging
import time
import psutil
from pathlib import Path

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_application_startup_performance():
    """Test application startup performance and initialization time."""
    print("Testing application startup performance...")
    
    try:
        start_time = time.time()
        
        # Test core module imports
        from common.constants import APP_TITLE, ANALYSIS_MODES
        from common.session_state import initialize_session_state
        from utils.gpu_utils import initialize_gpu_system
        
        import_time = time.time() - start_time
        print(f"✅ Core modules imported in {import_time:.3f} seconds")
        
        # Test GPU system initialization
        gpu_start = time.time()
        gpu_available, backend, device_info = initialize_gpu_system()
        gpu_time = time.time() - gpu_start
        print(f"✅ GPU system initialized in {gpu_time:.3f} seconds ({backend})")
        
        # Test session state initialization
        session_start = time.time()
        # Mock streamlit session state
        class MockSessionState:
            def __init__(self):
                self._state = {}
            def get(self, key, default=None):
                return self._state.get(key, default)
            def __setitem__(self, key, value):
                self._state[key] = value
            def __contains__(self, key):
                return key in self._state
        
        import streamlit as st
        if not hasattr(st, 'session_state'):
            st.session_state = MockSessionState()
            
        initialize_session_state()
        session_time = time.time() - session_start
        print(f"✅ Session state initialized in {session_time:.3f} seconds")
        
        total_time = time.time() - start_time
        print(f"✅ Total startup time: {total_time:.3f} seconds")
        
        # Performance criteria
        if total_time < 5.0:
            print("✅ Startup performance: EXCELLENT (< 5s)")
        elif total_time < 10.0:
            print("✅ Startup performance: GOOD (< 10s)")
        else:
            print("⚠️ Startup performance: NEEDS IMPROVEMENT (> 10s)")
        
        return True
        
    except Exception as e:
        print(f"❌ Startup performance test error: {e}")
        return False

def test_memory_efficiency():
    """Test memory usage efficiency and optimization."""
    print("\nTesting memory efficiency...")
    
    try:
        import gc
        
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / (1024 * 1024)  # MB
        print(f"✅ Initial memory usage: {initial_memory:.1f} MB")
        
        # Test memory usage with different operations
        memory_tests = []
        
        # Test 1: Import all modules
        gc.collect()
        mem_before = process.memory_info().rss / (1024 * 1024)
        
        from utils import (
            gpu_utils, processing_gpu, data_utils, 
            visualization, export_utils, general_utils
        )
        
        mem_after = process.memory_info().rss / (1024 * 1024)
        memory_tests.append(("Module imports", mem_after - mem_before))
        
        # Test 2: Create mock data
        gc.collect()
        mem_before = process.memory_info().rss / (1024 * 1024)
        
        import numpy as np
        mock_data = np.random.randn(10000, 1000).astype(np.float32)
        
        mem_after = process.memory_info().rss / (1024 * 1024)
        memory_tests.append(("Mock data creation", mem_after - mem_before))
        
        # Test 3: Processing configuration
        gc.collect()
        mem_before = process.memory_info().rss / (1024 * 1024)
        
        from utils.gpu_utils import get_optimal_processing_config
        configs = []
        for mode in ["Single inline (all crosslines)", "By inline/crossline section (AOI)"]:
            for traces in [1000, 10000, 50000]:
                configs.append(get_optimal_processing_config(mode, traces))
        
        mem_after = process.memory_info().rss / (1024 * 1024)
        memory_tests.append(("Processing configs", mem_after - mem_before))
        
        # Clean up
        del mock_data
        gc.collect()
        
        # Report memory usage
        print("\n📊 Memory Usage by Operation:")
        for operation, memory_delta in memory_tests:
            print(f"  {operation:<20}: {memory_delta:>6.1f} MB")
        
        final_memory = process.memory_info().rss / (1024 * 1024)
        total_usage = final_memory - initial_memory
        print(f"✅ Total memory increase: {total_usage:.1f} MB")
        
        # Memory efficiency criteria
        if total_usage < 50:
            print("✅ Memory efficiency: EXCELLENT (< 50 MB)")
        elif total_usage < 100:
            print("✅ Memory efficiency: GOOD (< 100 MB)")
        else:
            print("⚠️ Memory efficiency: NEEDS IMPROVEMENT (> 100 MB)")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory efficiency test error: {e}")
        return False

def test_processing_optimization():
    """Test processing efficiency and optimization."""
    print("\nTesting processing optimization...")
    
    try:
        from utils.gpu_utils import get_optimal_processing_config, get_processing_functions
        import numpy as np
        
        # Test different processing scenarios
        scenarios = [
            ("Small inline", "Single inline (all crosslines)", 500),
            ("Medium AOI", "By inline/crossline section (AOI)", 5000),
            ("Large polyline", "By Polyline File Import", 20000),
            ("Well markers", "By well markers", 100)
        ]
        
        print("\n📈 Processing Optimization Results:")
        print("Scenario              | Mode                    | Traces | Backend | Batch | Est.Time")
        print("-" * 85)
        
        for name, mode, traces in scenarios:
            config = get_optimal_processing_config(mode, traces)
            
            # Estimate processing time
            time_per_trace = 0.001 if config['backend'] == 'GPU' else 0.005  # seconds
            estimated_time = traces * time_per_trace
            
            print(f"{name:<20} | {mode:<23} | {traces:>6} | {config['backend']:>7} | "
                  f"{config['batch_size']:>5} | {estimated_time:>7.1f}s")
        
        # Test batch size optimization
        processing_funcs = get_processing_functions()
        print(f"\n✅ Processing backend: {processing_funcs['backend']}")
        print(f"✅ Single trace function available: {processing_funcs['single'] is not None}")
        print(f"✅ Batch function available: {processing_funcs['batch'] is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Processing optimization test error: {e}")
        return False

def test_user_experience_optimization():
    """Test user experience optimizations."""
    print("\nTesting user experience optimization...")
    
    try:
        # Test navigation and workflow
        from common.constants import ANALYSIS_MODES, AVAILABLE_OUTPUTS_ALL_MODES
        
        print(f"✅ Analysis modes available: {len(ANALYSIS_MODES)}")
        print(f"✅ Output types available: {len(AVAILABLE_OUTPUTS_ALL_MODES)}")
        
        # Test page module availability
        page_modules = [
            "pages/1_load_data.py",
            "pages/2_configure_display.py",
            "pages/3_select_area.py",
            "pages/4_analyze_data.py",
            "pages/5_export_results.py"
        ]
        
        for page in page_modules:
            if Path(page).exists():
                print(f"✅ Page module available: {page}")
            else:
                print(f"❌ Page module missing: {page}")
                return False
        
        # Test utility functions
        utility_modules = [
            "utils/gpu_utils.py",
            "utils/processing_gpu.py",
            "utils/data_utils.py",
            "utils/visualization.py",
            "utils/export_utils.py"
        ]
        
        for util in utility_modules:
            if Path(util).exists():
                print(f"✅ Utility module available: {util}")
            else:
                print(f"❌ Utility module missing: {util}")
                return False
        
        # Test error handling
        try:
            from utils.gpu_utils import initialize_gpu_system
            # This should not raise an exception even without GPU
            gpu_available, backend, device_info = initialize_gpu_system()
            print("✅ Error handling: GPU initialization graceful fallback")
        except Exception as e:
            print(f"❌ Error handling: GPU initialization failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ User experience optimization test error: {e}")
        return False

def test_deployment_readiness():
    """Test deployment readiness and configuration."""
    print("\nTesting deployment readiness...")
    
    try:
        # Check required files
        required_files = [
            "app.py",
            "requirements.txt",
            "common/__init__.py",
            "utils/__init__.py",
            "pages/__init__.py"
        ]
        
        for file_path in required_files:
            if Path(file_path).exists():
                print(f"✅ Required file present: {file_path}")
            else:
                print(f"❌ Required file missing: {file_path}")
                return False
        
        # Test configuration consistency
        from common.constants import APP_TITLE, ANALYSIS_MODES
        from utils.gpu_utils import GPU_AVAILABLE
        
        print(f"✅ Application title: {APP_TITLE}")
        print(f"✅ Analysis modes configured: {len(ANALYSIS_MODES)}")
        print(f"✅ GPU system status: {'Available' if GPU_AVAILABLE else 'CPU Fallback'}")
        
        # Test import structure
        try:
            import common
            import utils
            import pages
            print("✅ Package structure: All packages importable")
        except ImportError as e:
            print(f"❌ Package structure: Import error: {e}")
            return False
        
        # Test documentation
        doc_files = [
            "refactoring_guideline_details.md",
            "summary_step_05.md",
            "next_step_05_phase_06.md"
        ]
        
        doc_count = sum(1 for doc in doc_files if Path(doc).exists())
        print(f"✅ Documentation files: {doc_count}/{len(doc_files)} present")
        
        return True
        
    except Exception as e:
        print(f"❌ Deployment readiness test error: {e}")
        return False

def main():
    """Run all performance optimization tests."""
    print("=" * 70)
    print("WOSS Seismic Analysis Tool - Performance Optimization Test")
    print("=" * 70)
    
    tests = [
        ("Application Startup Performance", test_application_startup_performance),
        ("Memory Efficiency", test_memory_efficiency),
        ("Processing Optimization", test_processing_optimization),
        ("User Experience Optimization", test_user_experience_optimization),
        ("Deployment Readiness", test_deployment_readiness)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"Performance Optimization Results: {passed}/{total} tests passed")
    print("=" * 70)
    
    if passed == total:
        print("🎉 All performance optimization tests PASSED!")
        print("🚀 Application is ready for production deployment!")
        return 0
    else:
        print("⚠️ Some performance optimization tests FAILED.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
