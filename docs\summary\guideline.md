Okay, this is a great goal! Breaking down your monolithic Streamlit app into a more modular, page-by-page (or step-by-step) structure will significantly improve maintainability, testability, and the ability to inject new processing methods.

Here's a step-by-step plan to achieve this:

**Core Idea:**
We'll create separate Python files for each "step" or "page" of your application. The main `app.py` will become a router that calls the appropriate page function based on `st.session_state.current_step`. Each page function will handle its own UI and logic, interacting with `st.session_state` to pass data and control flow.

**Step 0: Preparation & Setup**

1.  **Backup:** Create a backup of your entire project.
2.  **Directory Structure:**
    ```
    your_project_root/
    ├── app.py                  # Main router
    ├── pages/                  # New directory for page modules
    │   ├── load_data_page.py
    │   ├── configure_display_page.py
    │   ├── select_mode_page.py
    │   ├── select_traces_page.py
    │   ├── configure_export_page.py # For AOI export config
    │   ├── view_results_page.py
    │   ├── export_process_page.py   # For AOI export processing
    │   └── download_export_page.py  # For AOI download
    ├── utils/
    │   ├── __init__.py
    │   ├── data_utils.py
    │   ├── visualization.py
    │   ├── processing.py
    │   ├── export_utils.py        # You might not need this if UI is in pages
    │   ├── dlogst_spec_descriptor_gpu.py
    │   └── general_utils.py       # For parse_polyline_string, etc. from utils.py
    ├── common/                 # New directory for shared components
    │   ├── __init__.py
    │   ├── constants.py           # APP_TITLE, AVAILABLE_OUTPUTS, etc.
    │   ├── session_state_manager.py # Initialize and manage session state keys
    │   └── ui_elements.py         # e.g., slider_with_number_input
    └── ... (other files like README, requirements.txt)
    ```
3.  **`common/constants.py`:**
    *   Move `APP_TITLE`, `AVAILABLE_OUTPUTS_SINGLE/MULTI/SECTION`, `EXPORTABLE_ATTR_INTERNAL_NAMES`, `EXPORTABLE_ATTR_DISPLAY_NAMES`, `ATTR_NAME_MAP`, `REVERSE_ATTR_NAME_MAP`, `DESCRIPTOR_LIMITS` here.
4.  **`common/session_state_manager.py`:**
    *   Create a function `initialize_session_state()` that contains the large block of `if 'key' not in st.session_state:` initializations.
    *   You can also define constants for your session state keys here to avoid magic strings, e.g., `KEY_CURRENT_STEP = "current_step"`.
    *   Move the `reset_state()` function here.
5.  **`common/ui_elements.py`:**
    *   Move `slider_with_number_input` here.
6.  **`utils/general_utils.py`:**
    *   Move functions from your current `utils.py` (like `parse_polyline_string`, `distance_point_to_segment`, `find_traces_near_polyline`, `get_suggested_batch_size_for_export`, `custom_excepthook`) into this new file to avoid confusion with the directory name.
    *   The `get_suggested_batch_size` (GPU memory based one) can also go here or in `processing.py` if it's more tied to processing logic.

**Step 1: Refactor `app.py` (The Router)**

Your `app.py` will be drastically simplified:

```python
# app.py
import streamlit as st
from common import constants, session_state_manager
from pages import (
    load_data_page,
    configure_display_page,
    select_mode_page,
    select_traces_page,
    configure_export_page,
    view_results_page,
    export_process_page,
    download_export_page
)
import sys
# from utils.general_utils import custom_excepthook # If you use it

# sys.excepthook = custom_excepthook # Optional

st.set_page_config(page_title=constants.APP_TITLE, layout="wide")
session_state_manager.initialize_session_state()

st.title(constants.APP_TITLE)

# Sidebar for "Start New Analysis" - common to all pages
st.sidebar.markdown("---")
if st.sidebar.button("🔄 Start New Analysis", use_container_width=True):
    session_state_manager.reset_state()
    st.success("Starting new analysis. All temporary data has been cleared.")
    st.rerun()

# Routing logic
if st.session_state.current_step == "load_data":
    load_data_page.render()
elif st.session_state.current_step == "configure_display":
    configure_display_page.render()
elif st.session_state.current_step == "select_mode":
    select_mode_page.render()
elif st.session_state.current_step == "select_traces":
    select_traces_page.render()
elif st.session_state.current_step == "configure_export": # AOI export config
    configure_export_page.render()
elif st.session_state.current_step == "view_results":
    view_results_page.render()
elif st.session_state.current_step == "export_process": # AOI export process
    export_process_page.render()
elif st.session_state.current_step == "download_export": # AOI download
    download_export_page.render()
else:
    st.info("Welcome! Please start by loading data.")
    if st.session_state.current_step != "load_data": # Should not happen
        session_state_manager.reset_state()
        st.rerun()
```

**Step 2: Create Page Modules (Example: `pages/load_data_page.py`)**

For each step in your original `if/elif` block, create a corresponding `[step_name]_page.py` file in the `pages/` directory. Each file will have a main function, typically named `render()`.

```python
# pages/load_data_page.py
import streamlit as st
import os
import tempfile
import logging
from common import constants, session_state_manager # Assuming reset_state is here
from utils.data_utils import (
    load_segy_headers_cached, get_sampling_interval, get_trace_count, load_excel_data_cached
)

def render():
    st.header("Step 1: Load Data")
    st.sidebar.header("Data Loading") # This will now appear *below* "Start New Analysis"

    # === SEG-Y File Upload ===
    st.sidebar.subheader("Upload SEG-Y File")
    segy_file = st.sidebar.file_uploader("Choose a SEG-Y file", type=["sgy", "segy"], key="p1_segy_file_uploader")
    if segy_file is not None:
        st.session_state.segy_file_info = {
            'name': segy_file.name,
            'buffer': segy_file
        }
        # Don't show success message here, show it after successful load_data_button click.

    # === Well Data Upload (Optional) ===
    st.sidebar.subheader("Upload Well Data (Optional)")
    st.session_state.use_wells = st.sidebar.checkbox("Use Well Data", value=st.session_state.get('use_wells', True), key="p1_use_wells_cb")

    well_file = st.sidebar.file_uploader("Choose an Excel file", type=["xls", "xlsx"], key="p1_well_file_uploader", disabled=not st.session_state.use_wells)
    if well_file is not None:
        st.session_state.well_file_info = {
            'name': well_file.name,
            'buffer': well_file
        }

    # === SEG-Y Header Bytes ===
    st.sidebar.subheader("SEG-Y Header Bytes")
    st.session_state.inline_byte = st.sidebar.number_input("Inline Byte", value=st.session_state.get('inline_byte', 189), step=1, key="p1_inline_byte")
    # ... (all other number_inputs for bytes, add unique keys like "p1_xline_byte")
    st.session_state.xline_byte = st.sidebar.number_input("Crossline Byte", value=st.session_state.get('xline_byte', 193), step=1, key="p1_xline_byte")
    st.session_state.x_byte = st.sidebar.number_input("X Coordinate Byte", value=st.session_state.get('x_byte', 73), step=1, key="p1_x_byte")
    st.session_state.y_byte = st.sidebar.number_input("Y Coordinate Byte", value=st.session_state.get('y_byte', 77), step=1, key="p1_y_byte")


    # === Coordinate Scaler Options ===
    st.sidebar.subheader("Coordinate Scaler")
    st.session_state.scaler_mode = st.sidebar.radio(
        "Scaler Mode",
        options=["Use Scaler Byte", "Custom Scaler"],
        index=0 if st.session_state.get('scaler_mode', "Use Scaler Byte") == "Use Scaler Byte" else 1,
        key="p1_scaler_mode_radio"
    )

    if st.session_state.scaler_mode == "Use Scaler Byte":
        st.session_state.scaler_byte = st.sidebar.number_input("Scaler Byte", value=st.session_state.get('scaler_byte', 71), step=1, key="p1_scaler_byte_num")
    else:
        st.session_state.custom_scaler = st.sidebar.number_input(
            "Custom Scaler Value",
            value=st.session_state.get('custom_scaler', 1.0),
            min_value=-1000.0,
            max_value=1000.0,
            step=0.1,
            help="Positive value: multiply coordinates by this value. Negative value: divide coordinates by the absolute value.",
            key="p1_custom_scaler_num"
        )

    # === Load Data Button ===
    if st.sidebar.button("Load Data", key="p1_load_data_button"):
        if st.session_state.get('segy_file_info'):
            tmp_file_path = None
            try:
                with tempfile.NamedTemporaryFile(delete=False, suffix=".sgy") as tmp_file:
                    tmp_file.write(st.session_state.segy_file_info['buffer'].getvalue())
                    tmp_file_path = tmp_file.name
                st.session_state.segy_temp_file_path = tmp_file_path
                logging.info(f"Created temporary SEG-Y file: {tmp_file_path}")

                header_loader = load_segy_headers_cached(
                    tmp_file_path,
                    st.session_state.inline_byte,
                    st.session_state.xline_byte,
                    st.session_state.x_byte,
                    st.session_state.y_byte,
                    st.session_state.scaler_mode,
                    scaler_byte=st.session_state.get('scaler_byte') if st.session_state.scaler_mode == "Use Scaler Byte" else None,
                    custom_scaler=st.session_state.get('custom_scaler') if st.session_state.scaler_mode == "Custom Scaler" else None
                )

                if header_loader:
                    st.session_state.header_loader = header_loader
                    st.session_state.dt = get_sampling_interval(st.session_state.segy_temp_file_path)
                    st.session_state.trace_count = get_trace_count(st.session_state.segy_temp_file_path)
                    
                    st.success(f"SEG-Y: {st.session_state.segy_file_info['name']} loaded. dt: {st.session_state.dt*1000:.2f} ms, Traces: {st.session_state.trace_count}")
                    logging.info(f"SEG-Y headers loaded. dt={st.session_state.dt}, trace_count={st.session_state.trace_count}")

                    if st.session_state.use_wells and st.session_state.get('well_file_info'):
                        well_df = load_excel_data_cached(st.session_state.well_file_info)
                        if well_df is not None:
                            st.session_state.well_df = well_df
                            st.success(f"Well data: {st.session_state.well_file_info['name']} loaded.")
                            logging.info(f"Well data loaded.")
                        else:
                            st.warning("Well file provided but failed to load. Proceeding without well data.")
                            st.session_state.well_df = None
                    else:
                        st.session_state.well_df = None

                    st.session_state.current_step = "configure_display"
                    st.rerun()
                # else: error handled in cached function

            except Exception as e:
                st.error(f"An unexpected error occurred during data loading: {e}")
                logging.error(f"Data loading failed: {e}", exc_info=True)
                if tmp_file_path and os.path.exists(tmp_file_path):
                    try:
                        os.remove(tmp_file_path)
                        st.session_state.segy_temp_file_path = None
                    except OSError as clean_e:
                         logging.warning(f"Could not clean up temp file {tmp_file_path} after error: {clean_e}")
        else:
            st.warning("Please upload a SEG-Y file.")

    # Instructions or status in the main area for this page
    st.markdown("""
    ### Instructions for Step 1:
    1.  **Upload SEG-Y File:** Select your seismic data.
    2.  **Upload Well Data (Optional):** If you have well information in Excel format, upload it.
    3.  **Configure SEG-Y Header Bytes:** Specify the byte locations for inline, crossline, and coordinates.
    4.  **Set Coordinate Scaler:** Choose how coordinate scaling should be applied.
    5.  Click **Load Data**.
    """)
    if st.session_state.get('header_loader'):
        st.success("Data loaded. Proceed to the next step using the sidebar or by confirming settings if they appear below.")
```

**Key changes in `load_data_page.py`:**
*   Imports are specific to this page's needs.
*   All UI elements get unique keys (e.g., `key="p1_segy_file_uploader"`) to prevent conflicts if similar widgets are used on other "pages" (even in a single-page app, this is good practice for clarity).
*   It reads from and writes to `st.session_state` to manage data and control flow (e.g., setting `st.session_state.current_step = "configure_display"`).
*   The main content area can now have specific instructions or status for this step.

**Step 3: Repeat for All Other Steps**

Create `render()` functions for `configure_display_page.py`, `select_mode_page.py`, etc., following the same pattern:
1.  **Copy relevant code block** from the monolithic `app.py`.
2.  **Adapt imports:** Only import what that page needs. Utility functions will come from `utils.*`, constants from `common.constants`, etc.
3.  **Unique Keys:** Ensure all Streamlit widgets have unique keys (e.g., `p2_some_slider` for page 2).
4.  **Session State:** All data shared between steps *must* go through `st.session_state`.
5.  **Navigation:** Page transitions happen by changing `st.session_state.current_step` and calling `st.rerun()`.
6.  **GPU Logic:** For pages that use GPU functions (`select_traces_page.py`, `export_process_page.py`), they will import `dlogst_spec_descriptor_gpu` and check `constants.GPU_AVAILABLE` (you'll need to move `GPU_AVAILABLE` logic to `common/constants.py` or compute it there).

**Example: `pages/configure_display_page.py` (Skeleton)**
```python
# pages/configure_display_page.py
import streamlit as st
import numpy as np
import segyio # For getting SEGY info
from common import constants, session_state_manager, ui_elements
from utils import data_utils, visualization # For plot_basemap_with_wells
from utils import processing # For calculate_stats_and_defaults
import plotly.colors as pcolors # for colormap list
import logging

def render():
    st.header("Step 2: Configure Analysis Parameters")
    # This page will have its own sidebar section header if needed,
    # but common controls like "Start New Analysis" are in the main app.py
    # st.sidebar.header("Display Configuration") # Redundant if common sidebar is used

    # --- Basemap Expander ---
    with st.expander("Basemap & SEGY Info", expanded=True):
        # ... (code for SEGY Info display and Basemap from monolithic app) ...
        # Make sure to use st.session_state for all values
        # Ensure all widget keys are unique, e.g., key="p2_show_basemap_controls_checkbox"
        # Call visualization.plot_basemap_with_wells
        pass # Replace with actual code

    # --- Parameter Configuration Expander ---
    with st.expander("Calculate Defaults & Configure Display", expanded=False):
        # ... (code for Parameter Configuration, Calculate Defaults button) ...
        # Call ui_elements.slider_with_number_input
        # Call processing.calculate_stats_and_defaults
        pass # Replace with actual code

        if st.session_state.get('stats_defaults') is not None:
            # ... (code for displaying tabs: General, Spectral Descriptors, Statistic) ...
            # This section involves many number_input and selectbox widgets.
            # Ensure all keys are unique.
            # Example for one descriptor limit:
            # with tab_spectral:
            #   st.session_state.plot_settings['hfc_cmap_min'] = st.number_input(
            #       "Min",
            #       value=float(st.session_state.plot_settings.get('hfc_cmap_min', HFC_DEFAULT_MIN)),
            #       key="p2_hfc_cmap_min_num", # Unique key
            #       format="%.3f"
            #   )
            pass # Replace with actual code

    # Navigation
    if st.session_state.get('stats_defaults') is not None:
        if st.button("Confirm Settings & Proceed", key="p2_confirm_settings_button"):
            st.session_state.current_step = "select_mode"
            logging.info("Settings confirmed, proceeding to mode selection.")
            st.rerun()
    else:
        st.info("Load data and click 'Calculate Defaults & Configure Display' to proceed.")
```

**Step 4: Utility Modules (`data_utils.py`, `visualization.py`, etc.)**

*   These should *not* contain Streamlit UI elements (like `st.button`, `st.file_uploader`). Their purpose is pure data processing or plot generation.
*   Functions like `get_custom_bytes_and_scaler` (which showed a Tkinter dialog) in `SegyHeaderLoader` are problematic. The GUI for byte input is now part of `load_data_page.py`. The `load_headers` method should just take the byte values as arguments.
*   Similarly, `select_surfaces` and `select_well_marker_pairs` from `data_utils.py` that use Tkinter should be replaced by Streamlit widgets (`st.multiselect`) in the relevant page module (e.g., `select_mode_page.py` or `select_traces_page.py`). The helper functions in `data_utils.py` can be simplified to just return the data needed to populate these widgets (e.g., `get_surfaces_streamlit` which returns a list of surface names).
*   Plotting functions in `visualization.py` should return Plotly `Figure` objects, not call `fig.show()`. The page module will then use `st.plotly_chart(fig)`.

**Step 5: Injecting Other Processing Methods**

With this modular structure:
1.  **New Descriptor Calculation:**
    *   If it's a new standalone descriptor, add its calculation logic (e.g., a new Python file in `utils/` or `processing_methods/`).
    *   Modify `pages/select_traces_page.py` (and `pages/export_process_page.py` for AOI) to call this new calculation.
    *   Add the new descriptor's name to `common/constants.py` (e.g., in `AVAILABLE_OUTPUTS_...`).
    *   Update `pages/configure_display_page.py` to allow setting display limits/colormaps for this new descriptor.
    *   Update `pages/view_results_page.py` to handle plotting this new descriptor.

2.  **Alternative Full Workflow Step:**
    *   You could define an interface (e.g., a base class or a set of expected functions) for a "processing plugin."
    *   A new plugin could implement these functions for its specific steps.
    *   The main `app.py` or page modules could then dynamically load or select which plugin's functions to call for certain steps. This is more advanced but offers maximum flexibility. For example, instead of `select_traces_page.render()`, you might have `current_plugin.render_select_traces_page()`.

**Important Considerations:**

*   **Widget Keys:** Every interactive Streamlit widget (`st.button`, `st.slider`, `st.text_input`, etc.) needs a unique `key` across the entire application, especially when you conditionally render them or have them in different "pages" of a single-page app. Prefix keys with page identifiers (e.g., `key="p1_my_button"` for page 1).
*   **State Reset:** The `reset_state()` function in `common/session_state_manager.py` is critical. Ensure it clears *all* relevant session state keys when starting a new analysis.
*   **Imports:** Be diligent about making imports local to the module that needs them. Avoid circular dependencies.
*   **Testing:** Test each page/step thoroughly after refactoring.
*   **Logging:** Centralized logging setup (as you have) is good. Page-specific logging messages can help debug.

This refactoring is a significant undertaking but will pay off in the long run. Proceed step-by-step, testing frequently. Good luck!