# WOSS Seismic Analysis Tool: Detailed Refactoring Implementation Guide

## Executive Summary

This document provides a comprehensive, phase-by-phase implementation plan for refactoring the monolithic WOSS Seismic Analysis Tool into a modular, maintainable Streamlit application. The refactoring emphasizes **GPU-only processing** for all analysis modes (inline, crossline, AOI, and polyline) as specified.

## Project Overview

**Current State:** A single `app_ref.py` file (~4600+ lines) containing all UI, business logic, and processing code
**Target State:** Modular architecture with clear separation of concerns, GPU-optimized processing, and maintainable codebase

### Key Requirements
- **GPU-Only Processing:** All spectral analysis must use GPU processing via CuPy
- **Modular Architecture:** Separation of UI, business logic, and utilities
- **Support All Modes:** Inline, crossline, AOI, and polyline area selection
- **Maintainable:** Easy to modify, test, and extend
- **Performance:** Leverage GPU acceleration for all computationally intensive tasks

---

## Phase 1: Foundation Setup (Duration: 1-2 days)

### Phase 1.1: Directory Structure Creation

**Objective:** Establish the modular directory structure

**Actions:**
1. Create the following directory structure:
```
OSST_spectrum-main/
├── app.py                          # Main application router
├── common/                         # Shared resources
│   ├── __init__.py
│   ├── constants.py               # Application constants
│   ├── session_state.py          # Session state management
│   └── ui_elements.py            # Reusable UI components
├── pages/                         # Page modules
│   ├── __init__.py
│   ├── load_data_page.py         # Step 1: Data loading
│   ├── configure_display_page.py # Step 2: Parameter configuration
│   ├── select_area_page.py       # Step 3: Area selection
│   ├── analyze_data_page.py      # Step 4: GPU processing
│   ├── view_results_page.py      # Step 5: Results visualization
│   ├── configure_export_page.py  # Step 6A: Export configuration
│   ├── export_process_page.py    # Step 6B: Export processing
│   └── download_export_page.py   # Step 6C: Download results
├── utils/                         # Backend utilities
│   ├── __init__.py
│   ├── data_utils.py             # Data loading and parsing
│   ├── gpu_utils.py              # GPU initialization and management
│   ├── processing.py             # Core processing functions
│   ├── visualization.py          # Plotting functions
│   ├── export_utils.py           # Export utilities
│   └── general_utils.py          # General helper functions
└── requirements.txt               # Dependencies
```

2. Create empty `__init__.py` files in all directories

**Success Criteria:**
- [ ] Directory structure created
- [ ] All `__init__.py` files present
- [ ] No import errors when running Python in the root directory

### Phase 1.2: Constants and Configuration

**Objective:** Centralize all application constants

**File:** `common/constants.py`

**Content:**
```python
# Application Configuration
APP_TITLE = "WOSS Seismic Analysis Tool"

# GPU Processing Settings
GPU_REQUIRED = True  # Force GPU-only processing
DEFAULT_BATCH_SIZE = 512
DEFAULT_EXPORT_BATCH_SIZE = 50

# Analysis Modes
ANALYSIS_MODES = [
    "By well markers",
    "Single inline (all crosslines)", 
    "Single crossline (all inlines)",
    "By inline/crossline section (AOI)",
    "By Polyline File Import"
]

# Available Outputs for Different Modes
AVAILABLE_OUTPUTS_SINGLE = [
    "Input Signal", "HFC", "Spectral Decrease", "Spectral Slope", 
    "Mag*Voice Slope", "Voice Slope", "Peak Frequency", 
    "Spectral Centroid", "Dominant Frequency", "Spectral Bandwidth",
    "Spectral Rolloff", "Magnitude Spectrogram", "Magnitude * Voice", 
    "WOSS", "Normalized Dominant Frequency"
]

AVAILABLE_OUTPUTS_MULTI = [
    "Input Signal", "HFC", "Spectral Decrease", "Spectral Slope",
    "Mag*Voice Slope", "Voice Slope", "Peak Frequency",
    "Spectral Centroid", "Dominant Frequency", "WOSS",
    "Normalized Dominant Frequency"
]

AVAILABLE_OUTPUTS_SECTION = [
    "Input Signal", "HFC", "Spectral Decrease", "Spectral Slope",
    "Mag*Voice Slope", "Voice Slope", "Peak Frequency",
    "Spectral Centroid", "Dominant Frequency", "Spectral Bandwidth",
    "Spectral Rolloff", "WOSS", "Normalized Dominant Frequency"
]

# Export Attributes
EXPORTABLE_ATTR_INTERNAL_NAMES = [
    "data", "peak_freq", "spec_centroid", "fdom", "spec_slope",
    "mag_voice_slope", "voice_slope", "spec_decrease", "norm_peak_freq",
    "norm_spec_centroid", "norm_fdom", "hfc", "spec_bandwidth", 
    "spec_rolloff", "WOSS"
]

EXPORTABLE_ATTR_DISPLAY_NAMES = [
    "Original Data", "Peak Frequency", "Spectral Centroid",
    "Dominant Frequency", "Spectral Slope", "Mag*Voice Slope",
    "Voice Slope", "Spectral Decrease", "Normalized Peak Frequency",
    "Normalized Spectral Centroid", "Normalized Dominant Frequency",
    "HFC", "Spectral Bandwidth", "Spectral Rolloff",
    "WOSS (Weighted-Optimum Spectral Shape)"
]

# Descriptor Display Limits
DESCRIPTOR_LIMITS = {
    "Input Signal": {"min": None, "max": None},
    "HFC": {"min": 0.0, "max": None},
    "Spectral Decrease": {"min": 0.0, "max": None},
    "Spectral Slope": {"min": None, "max": None},
    "Mag*Voice Slope": {"min": None, "max": None},
    "Spectral Bandwidth": {"min": 0.0, "max": 50.0},
    "Spectral Rolloff": {"min": 0.0, "max": 50.0},
    "WOSS": {"min": -10.0, "max": 10.0},
    "Normalized Dominant Frequency": {"min": 0.0, "max": 125.0}
}

# Default Processing Parameters
DEFAULT_SPECTRAL_PARAMS = {
    'use_band_limited': False,
    'shape': 0.35,
    'kmax': 120.0,
    'int_val': 35.0,
    'b1': 5.0,
    'b2': 40.0,
    'p_bandwidth': 2.0,
    'roll_percent': 0.80,
    'epsilon': 1e-10,
    'fdom_exponent': 2.0
}

# SEGY Header Defaults
DEFAULT_SEGY_HEADERS = {
    'inline_byte': 189,
    'xline_byte': 193,
    'x_byte': 181,
    'y_byte': 185,
    'scaler_byte': 71
}
```

**Success Criteria:**
- [ ] All constants moved from `app_ref.py`
- [ ] No hardcoded values remain in processing code
- [ ] Constants are easily configurable

### Phase 1.3: Session State Management

**Objective:** Centralize session state initialization and management

**File:** `common/session_state.py`

**Content:**
```python
import streamlit as st
import tempfile
import os
import shutil
import logging
from .constants import DEFAULT_SPECTRAL_PARAMS, DEFAULT_SEGY_HEADERS

def initialize_session_state():
    """Initialize all session state variables with default values."""
    
    # Workflow control
    if 'current_step' not in st.session_state:
        st.session_state.current_step = "load_data"
    
    # File handling
    if 'segy_file_info' not in st.session_state:
        st.session_state.segy_file_info = None
    if 'well_file_info' not in st.session_state:
        st.session_state.well_file_info = None
    if 'polyline_file_info' not in st.session_state:
        st.session_state.polyline_file_info = None
    if 'segy_temp_file_path' not in st.session_state:
        st.session_state.segy_temp_file_path = None
    
    # Data objects
    if 'header_loader' not in st.session_state:
        st.session_state.header_loader = None
    if 'well_df' not in st.session_state:
        st.session_state.well_df = None
    if 'seismic' not in st.session_state:
        st.session_state.seismic = None
    
    # Processing parameters
    if 'dt' not in st.session_state:
        st.session_state.dt = None
    if 'trace_count' not in st.session_state:
        st.session_state.trace_count = None
    if 'plot_settings' not in st.session_state:
        st.session_state.plot_settings = DEFAULT_SPECTRAL_PARAMS.copy()
    if 'stats_defaults' not in st.session_state:
        st.session_state.stats_defaults = None
    
    # SEGY header configuration
    for key, value in DEFAULT_SEGY_HEADERS.items():
        if key not in st.session_state:
            st.session_state[key] = value
    
    # Coordinate scaling
    if 'scaler_mode' not in st.session_state:
        st.session_state.scaler_mode = "Use Scaler Byte"
    if 'custom_scaler' not in st.session_state:
        st.session_state.custom_scaler = 1.0
    if 'use_wells' not in st.session_state:
        st.session_state.use_wells = True
    
    # Analysis mode and selection
    if 'selection_mode' not in st.session_state:
        st.session_state.selection_mode = None
    if 'selected_well_markers' not in st.session_state:
        st.session_state.selected_well_markers = []
    if 'plot_mode_wells' not in st.session_state:
        st.session_state.plot_mode_wells = 1
    if 'plot_twt' not in st.session_state:
        st.session_state.plot_twt = False
    
    # Area selection parameters
    if 'selected_inline' not in st.session_state:
        st.session_state.selected_inline = None
    if 'selected_crossline' not in st.session_state:
        st.session_state.selected_crossline = None
    if 'aoi_inline_min' not in st.session_state:
        st.session_state.aoi_inline_min = None
    if 'aoi_inline_max' not in st.session_state:
        st.session_state.aoi_inline_max = None
    if 'aoi_xline_min' not in st.session_state:
        st.session_state.aoi_xline_min = None
    if 'aoi_xline_max' not in st.session_state:
        st.session_state.aoi_xline_max = None
    if 'polyline_vertices' not in st.session_state:
        st.session_state.polyline_vertices = None
    if 'polyline_tolerance' not in st.session_state:
        st.session_state.polyline_tolerance = 0.0
    
    # Processing results
    if 'selected_indices' not in st.session_state:
        st.session_state.selected_indices = []
    if 'loaded_trace_data' not in st.session_state:
        st.session_state.loaded_trace_data = []
    if 'calculated_descriptors' not in st.session_state:
        st.session_state.calculated_descriptors = []
    if 'analysis_complete' not in st.session_state:
        st.session_state.analysis_complete = False
    
    # Visualization and output
    if 'selected_outputs' not in st.session_state:
        st.session_state.selected_outputs = []
    if 'descriptor_statistics' not in st.session_state:
        st.session_state.descriptor_statistics = {}
    
    # Export configuration
    if 'export_attributes' not in st.session_state:
        st.session_state.export_attributes = []
    if 'export_grouping' not in st.session_state:
        st.session_state.export_grouping = None
    if 'export_batch_step' not in st.session_state:
        st.session_state.export_batch_step = 1
    if 'export_in_progress' not in st.session_state:
        st.session_state.export_in_progress = False
    if 'exported_files_info' not in st.session_state:
        st.session_state.exported_files_info = None
    if 'export_output_dir' not in st.session_state:
        st.session_state.export_output_dir = None
    
    # GPU and processing
    if 'batch_size' not in st.session_state:
        st.session_state.batch_size = None
    
    # UI state flags
    if 'traces_loaded_inline' not in st.session_state:
        st.session_state.traces_loaded_inline = False
    if 'traces_loaded_crossline' not in st.session_state:
        st.session_state.traces_loaded_crossline = False
    if 'traces_loaded_polyline' not in st.session_state:
        st.session_state.traces_loaded_polyline = False

def reset_state():
    """Reset all session state for a new analysis."""
    logging.info("Resetting session state for new analysis")
    
    # Clean up temporary files
    temp_paths = [
        st.session_state.get('segy_temp_file_path'),
        st.session_state.get('export_output_dir')
    ]
    
    for temp_path in temp_paths:
        if temp_path and os.path.exists(temp_path):
            try:
                if os.path.isfile(temp_path):
                    os.unlink(temp_path)
                elif os.path.isdir(temp_path):
                    shutil.rmtree(temp_path)
                logging.info(f"Cleaned up temporary path: {temp_path}")
            except Exception as e:
                logging.warning(f"Could not clean up {temp_path}: {e}")
    
    # Reset all session state keys
    keys_to_reset = [
        'segy_file_info', 'well_file_info', 'polyline_file_info',
        'header_loader', 'well_df', 'seismic', 'dt', 'trace_count',
        'stats_defaults', 'selected_well_markers', 'selected_inline',
        'selected_crossline', 'aoi_inline_min', 'aoi_inline_max',
        'aoi_xline_min', 'aoi_xline_max', 'polyline_vertices',
        'polyline_tolerance', 'selected_indices', 'loaded_trace_data',
        'calculated_descriptors', 'analysis_complete', 'selected_outputs',
        'descriptor_statistics', 'export_attributes', 'export_grouping',
        'export_batch_step', 'export_in_progress', 'exported_files_info',
        'export_output_dir', 'batch_size', 'segy_temp_file_path',
        'traces_loaded_inline', 'traces_loaded_crossline',
        'traces_loaded_polyline'
    ]
    
    for key in keys_to_reset:
        if key in st.session_state:
            if key.endswith('_data') and isinstance(st.session_state[key], list):
                st.session_state[key] = []
            else:
                st.session_state[key] = None
    
    # Reset plot settings to defaults
    st.session_state.plot_settings = DEFAULT_SPECTRAL_PARAMS.copy()
    
    # Reset workflow to beginning
    st.session_state.current_step = "load_data"
    
    logging.info("Session state reset complete")
```

**Success Criteria:**
- [ ] All session state initialization moved from `app_ref.py`
- [ ] Clean separation between initialization and reset
- [ ] Proper cleanup of temporary files

---

## Phase 2: Core Utilities Migration (Duration: 2-3 days)

### Phase 2.1: GPU Utilities Module

**Objective:** Centralize GPU initialization and management

**File:** `utils/gpu_utils.py`

**Content:**
```python
import streamlit as st
import torch
import logging
import numpy as np

# Global GPU availability flag
GPU_AVAILABLE = False

@st.cache_resource
def initialize_gpu_functions():
    """Initialize GPU functions and set global availability flag."""
    global GPU_AVAILABLE
    
    try:
        # Test PyTorch CUDA availability first
        if not torch.cuda.is_available():
            raise ImportError("CUDA not available through PyTorch")
        
        # Try to import CuPy-based functions
        from dlogst_spec_descriptor_gpu import (
            dlogst_spec_descriptor_gpu,
            dlogst_spec_descriptor_gpu_2d_chunked,
            dlogst_spec_descriptor_gpu_2d_chunked_mag
        )
        
        # Test basic GPU functionality
        import cupy as cp
        test_array = cp.array([1, 2, 3], dtype=cp.float32)
        _ = cp.sum(test_array)  # Simple operation to test GPU
        
        GPU_AVAILABLE = True
        st.sidebar.success("✅ GPU processing available")
        logging.info("GPU functions initialized successfully")
        
        return {
            'dlogst_spec_descriptor_gpu': dlogst_spec_descriptor_gpu,
            'dlogst_spec_descriptor_gpu_2d_chunked': dlogst_spec_descriptor_gpu_2d_chunked,
            'dlogst_spec_descriptor_gpu_2d_chunked_mag': dlogst_spec_descriptor_gpu_2d_chunked_mag,
            'available': True
        }
        
    except ImportError as e:
        GPU_AVAILABLE = False
        st.sidebar.error("❌ GPU processing unavailable")
        st.sidebar.error(f"Error: {e}")
        logging.error(f"GPU initialization failed: {e}")
        
        # Since GPU is required, we should not provide fallback functions
        return {
            'dlogst_spec_descriptor_gpu': None,
            'dlogst_spec_descriptor_gpu_2d_chunked': None, 
            'dlogst_spec_descriptor_gpu_2d_chunked_mag': None,
            'available': False
        }

def check_gpu_availability():
    """Check if GPU is available for processing."""
    return GPU_AVAILABLE

def get_gpu_memory_info():
    """Get GPU memory information if available."""
    if not GPU_AVAILABLE:
        return None
    
    try:
        import cupy as cp
        mempool = cp.get_default_memory_pool()
        return {
            'total_bytes': mempool.total_bytes(),
            'used_bytes': mempool.used_bytes(),
            'free_bytes': mempool.free_bytes()
        }
    except Exception as e:
        logging.warning(f"Could not get GPU memory info: {e}")
        return None

def suggest_batch_size(base_size=512):
    """Suggest optimal batch size based on available GPU memory."""
    if not GPU_AVAILABLE:
        return base_size
    
    try:
        memory_info = get_gpu_memory_info()
        if memory_info and memory_info['free_bytes'] > 0:
            # Simple heuristic: adjust batch size based on available memory
            free_gb = memory_info['free_bytes'] / (1024**3)
            if free_gb > 8:
                return min(1024, base_size * 2)
            elif free_gb < 2:
                return max(128, base_size // 2)
            else:
                return base_size
    except Exception as e:
        logging.warning(f"Error calculating batch size: {e}")
    
    return base_size

def clear_gpu_memory():
    """Clear GPU memory pools."""
    if not GPU_AVAILABLE:
        return
    
    try:
        import cupy as cp
        cp.get_default_memory_pool().free_all_blocks()
        logging.info("GPU memory cleared")
    except Exception as e:
        logging.warning(f"Could not clear GPU memory: {e}")
```

**Success Criteria:**
- [ ] GPU initialization moved from `app_ref.py`
- [ ] Clear error handling for GPU unavailability
- [ ] Memory management utilities available

### Phase 2.2: Data Utilities Migration

**Objective:** Move all data loading and parsing functions

**File:** `utils/data_utils.py`

**Migration Actions:**
1. Extract the `SegyHeaderLoader` class from existing code
2. Move SEGY import functions from `Load/` directory
3. Add well data loading functions
4. Ensure GPU-only compatibility

**Key Functions to Include:**
- `SegyHeaderLoader` class
- `load_excel_data()` 
- `get_nearest_trace_index()`
- `load_trace_sample()`
- `get_sampling_interval()`
- `get_trace_count()`
- `merge_segy_batch_files()`
- Streamlit-compatible well data functions

**Success Criteria:**
- [ ] All data loading functions centralized
- [ ] No direct Streamlit dependencies in utility functions
- [ ] Clear API for data access

### Phase 2.3: Processing Utilities Migration

**Objective:** Move core processing and WOSS calculation functions

**File:** `utils/processing.py`

**Key Functions to Migrate:**
```python
def calculate_woss(descriptor, plot_settings):
    """GPU-optimized WOSS calculation."""
    # Move from existing processing.py
    pass

def calculate_stats_and_defaults(segy_path, header_loader, dt, sample_percent, max_traces_for_stats, **spectral_params):
    """Calculate statistics using GPU processing only."""
    # Ensure only GPU functions are called
    pass

def process_traces_gpu_batch(trace_data_list, dt, plot_settings):
    """Process multiple traces using GPU batching."""
    # New function for efficient batch processing
    pass
```

**Success Criteria:**
- [ ] All processing functions use GPU-only
- [ ] WOSS calculation centralized
- [ ] Batch processing optimized

---

## Phase 3: Page Module Creation (Duration: 3-4 days)

### Phase 3.1: Load Data Page

**Objective:** Create the first page of the workflow

**File:** `pages/load_data_page.py`

**Key Features:**
```python
import streamlit as st
import tempfile
import os
import logging
from common.constants import DEFAULT_SEGY_HEADERS
from common.session_state import initialize_session_state
from utils.data_utils import load_segy_headers_cached, get_sampling_interval, get_trace_count, load_excel_data_cached
from utils.gpu_utils import check_gpu_availability
from Load.data_classes import SegyIO2D, SegyIO3D

def render():
    """Render the load data page."""
    st.header("Step 1: Load Seismic Data")
    
    # Check GPU availability first
    if not check_gpu_availability():
        st.error("🚫 GPU processing is required but not available. Please check your CUDA installation.")
        st.info("This application requires GPU processing for all analysis modes.")
        return
    
    st.sidebar.header("Data Loading")
    
    # SEG-Y file upload
    render_segy_upload()
    
    # Well data upload (optional)
    render_well_upload()
    
    # SEGY header configuration
    render_header_configuration()
    
    # Load data button
    render_load_button()

def render_segy_upload():
    """Render SEG-Y file upload section."""
    st.sidebar.subheader("Upload SEG-Y File")
    segy_file = st.sidebar.file_uploader(
        "Choose a SEG-Y file", 
        type=["sgy", "segy"], 
        key="segy_file_uploader"
    )
    
    if segy_file is not None:
        st.session_state.segy_file_info = {
            'name': segy_file.name,
            'buffer': segy_file
        }
        st.sidebar.success(f"✅ {segy_file.name} uploaded")

def render_well_upload():
    """Render well data upload section."""
    st.sidebar.subheader("Upload Well Data (Optional)")
    st.session_state.use_wells = st.sidebar.checkbox(
        "Use Well Data", 
        value=st.session_state.get('use_wells', True),
        key="use_wells_checkbox"
    )
    
    if st.session_state.use_wells:
        well_file = st.sidebar.file_uploader(
            "Choose well data file (Excel)", 
            type=["xlsx", "xls"],
            key="well_file_uploader"
        )
        
        if well_file is not None:
            st.session_state.well_file_info = {
                'name': well_file.name,
                'buffer': well_file
            }
            st.sidebar.success(f"✅ {well_file.name} uploaded")

def render_header_configuration():
    """Render SEGY header byte configuration."""
    st.sidebar.subheader("SEG-Y Header Configuration")
    
    # Header byte inputs
    st.session_state.inline_byte = st.sidebar.number_input(
        "Inline Byte", value=st.session_state.get('inline_byte', 189),
        min_value=1, max_value=240, key="inline_byte_input"
    )
    
    st.session_state.xline_byte = st.sidebar.number_input(
        "Crossline Byte", value=st.session_state.get('xline_byte', 193),
        min_value=1, max_value=240, key="xline_byte_input"
    )
    
    st.session_state.x_byte = st.sidebar.number_input(
        "X Coordinate Byte", value=st.session_state.get('x_byte', 181),
        min_value=1, max_value=240, key="x_byte_input"
    )
    
    st.session_state.y_byte = st.sidebar.number_input(
        "Y Coordinate Byte", value=st.session_state.get('y_byte', 185),
        min_value=1, max_value=240, key="y_byte_input"
    )
    
    # Coordinate scaling options
    st.session_state.scaler_mode = st.sidebar.selectbox(
        "Coordinate Scaling",
        ["Use Scaler Byte", "Custom Scaler", "No Scaling"],
        index=0, key="scaler_mode_select"
    )
    
    if st.session_state.scaler_mode == "Use Scaler Byte":
        st.session_state.scaler_byte = st.sidebar.number_input(
            "Scaler Byte", value=st.session_state.get('scaler_byte', 71),
            min_value=1, max_value=240, key="scaler_byte_input"
        )
    elif st.session_state.scaler_mode == "Custom Scaler":
        st.session_state.custom_scaler = st.sidebar.number_input(
            "Custom Scaler Value", value=st.session_state.get('custom_scaler', 1.0),
            min_value=-1000.0, max_value=1000.0, step=0.1,
            key="custom_scaler_input"
        )

def render_load_button():
    """Render the load data button and handle loading."""
    if st.sidebar.button("🔄 Load Data", key="load_data_button"):
        if not st.session_state.get('segy_file_info'):
            st.sidebar.error("Please upload a SEG-Y file first")
            return
        
        with st.spinner("Loading SEG-Y data..."):
            success = load_segy_data()
            
        if success:
            with st.spinner("Loading well data..."):
                load_well_data()
            
            st.sidebar.success("✅ Data loaded successfully!")
            st.session_state.current_step = "configure_display"
            st.rerun()

def load_segy_data():
    """Load and process SEG-Y data."""
    try:
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".sgy") as tmp_file:
            tmp_file.write(st.session_state.segy_file_info['buffer'].getvalue())
            tmp_file_path = tmp_file.name
        
        st.session_state.segy_temp_file_path = tmp_file_path
        
        # Determine if 2D or 3D
        # Implementation depends on your SEGY detection logic
        
        # Create SEGY object
        if st.session_state.get('seismic_type') == "2D":
            segyfile = SegyIO2D(tmp_file_path)
        else:
            segyfile = SegyIO3D(
                tmp_file_path, 
                st.session_state.inline_byte, 
                st.session_state.xline_byte
            )
        
        st.session_state.seismic = segyfile
        
        # Load headers using cached function
        header_loader = load_segy_headers_cached(
            tmp_file_path,
            st.session_state.inline_byte,
            st.session_state.xline_byte,
            st.session_state.x_byte,
            st.session_state.y_byte,
            st.session_state.scaler_mode,
            scaler_byte=st.session_state.get('scaler_byte') if st.session_state.scaler_mode == "Use Scaler Byte" else None,
            custom_scaler=st.session_state.get('custom_scaler') if st.session_state.scaler_mode == "Custom Scaler" else None
        )
        
        if header_loader:
            st.session_state.header_loader = header_loader
            st.session_state.dt = get_sampling_interval(tmp_file_path)
            st.session_state.trace_count = get_trace_count(tmp_file_path)
            
            logging.info(f"SEG-Y loaded: dt={st.session_state.dt}, traces={st.session_state.trace_count}")
            return True
        else:
            st.error("Failed to load SEG-Y headers")
            return False
            
    except Exception as e:
        st.error(f"Error loading SEG-Y file: {e}")
        logging.error(f"SEG-Y loading failed: {e}", exc_info=True)
        return False

def load_well_data():
    """Load well data if provided."""
    if not st.session_state.use_wells or not st.session_state.get('well_file_info'):
        st.session_state.well_df = None
        return
    
    try:
        well_df = load_excel_data_cached(st.session_state.well_file_info['buffer'])
        if well_df is not None and not well_df.empty:
            st.session_state.well_df = well_df
            logging.info(f"Well data loaded: {len(well_df)} records")
        else:
            st.session_state.well_df = None
            st.warning("Well file provided but failed to load")
    except Exception as e:
        st.session_state.well_df = None
        st.warning(f"Error loading well data: {e}")
        logging.error(f"Well data loading failed: {e}")
```

**Success Criteria:**
- [ ] Clean separation of UI and logic
- [ ] GPU availability check enforced
- [ ] All file uploads handled properly
- [ ] Smooth transition to next step

### Phase 3.2: Configure Display Page

**Objective:** Parameter configuration with GPU-based statistics

**File:** `pages/configure_display_page.py`

**Key Features:**
- Basemap generation with wells
- GPU-based statistics calculation
- Parameter configuration UI
- Real-time parameter validation

### Phase 3.3: Select Area Page

**Objective:** Unified area selection for all modes

**File:** `pages/select_area_page.py`

**Key Features:**
```python
def render():
    """Render area selection page with all modes."""
    st.header("Step 3: Select Analysis Area")
    
    # Mode selection
    render_mode_selection()
    
    # Mode-specific UI
    mode = st.session_state.selection_mode
    if mode == "By well markers":
        render_well_markers_mode()
    elif mode == "Single inline (all crosslines)":
        render_inline_mode()
    elif mode == "Single crossline (all inlines)":
        render_crossline_mode()
    elif mode == "By inline/crossline section (AOI)":
        render_aoi_mode()
    elif mode == "By Polyline File Import":
        render_polyline_mode()

def render_mode_selection():
    """Render analysis mode selection."""
    from common.constants import ANALYSIS_MODES
    
    st.session_state.selection_mode = st.sidebar.selectbox(
        "Select Analysis Mode",
        ANALYSIS_MODES,
        key="analysis_mode_select"
    )

def render_inline_mode():
    """Render single inline selection mode."""
    if not st.session_state.header_loader:
        st.error("No data loaded")
        return
    
    # Get inline range
    inlines = np.unique(st.session_state.header_loader.inlines)
    min_inline, max_inline = int(inlines.min()), int(inlines.max())
    
    st.subheader("Single Inline Mode")
    st.info("Select a single inline to analyze all crosslines along it")
    
    # Inline selection
    selected_inline = st.selectbox(
        "Select Inline",
        options=inlines,
        key="inline_select"
    )
    
    if st.button("🔄 Load Traces", key="load_inline_traces"):
        load_inline_traces(selected_inline)

def load_inline_traces(inline_number):
    """Load traces for selected inline using GPU processing."""
    try:
        with st.spinner(f"Loading traces for inline {inline_number}..."):
            # Find traces for this inline
            mask = st.session_state.header_loader.inlines == inline_number
            indices = st.session_state.header_loader.unique_indices[mask]
            
            st.session_state.selected_indices = indices.tolist()
            st.session_state.selected_inline = inline_number
            
            # Transition to GPU processing
            st.session_state.current_step = "analyze_data"
            st.rerun()
            
    except Exception as e:
        st.error(f"Error loading inline traces: {e}")
        logging.error(f"Inline trace loading failed: {e}")
```

### Phase 3.4: Analyze Data Page (GPU Processing)

**Objective:** GPU-only analysis for all modes

**File:** `pages/analyze_data_page.py`

**Key Features:**
```python
def render():
    """Main analysis page with GPU processing."""
    st.header("Step 4: GPU Analysis Processing")
    
    # Check GPU availability
    if not check_gpu_availability():
        st.error("GPU processing required but not available")
        return
    
    # Get analysis mode
    mode = st.session_state.selection_mode
    
    # Route to appropriate processing function
    if mode in ["Single inline (all crosslines)", "Single crossline (all inlines)"]:
        process_section_mode()
    elif mode == "By well markers":
        process_well_markers_mode()
    elif mode == "By inline/crossline section (AOI)":
        process_aoi_mode()
    elif mode == "By Polyline File Import":
        process_polyline_mode()

def process_section_mode():
    """Process section data using GPU batching."""
    st.info("🚀 Processing section data with GPU acceleration...")
    
    # Load trace data
    trace_data = load_selected_traces()
    if not trace_data:
        return
    
    # GPU batch processing
    with st.spinner("Calculating spectral descriptors on GPU..."):
        descriptors = process_traces_gpu_batch(trace_data)
    
    if descriptors:
        st.session_state.calculated_descriptors = descriptors
        st.session_state.analysis_complete = True
        st.success(f"✅ Processed {len(descriptors)} traces on GPU")
        
        # Transition to results
        st.session_state.current_step = "view_results"
        st.rerun()

def process_traces_gpu_batch(trace_data_list):
    """Process traces using GPU batch functions."""
    from utils.gpu_utils import initialize_gpu_functions
    from utils.processing import calculate_woss
    
    # Get GPU functions
    gpu_funcs = initialize_gpu_functions()
    if not gpu_funcs['available']:
        st.error("GPU functions not available")
        return None
    
    try:
        # Prepare data for GPU processing
        traces = [item['trace_sample'] for item in trace_data_list]
        
        # Determine max length for padding
        max_len = max(len(trace) for trace in traces)
        
        # Pad traces to uniform length
        padded_traces = []
        for trace in traces:
            if len(trace) < max_len:
                padded = np.pad(trace, (0, max_len - len(trace)), 'constant')
                padded_traces.append(padded)
            else:
                padded_traces.append(trace)
        
        # Stack for GPU processing
        trace_array = np.stack(padded_traces).astype(np.float32)
        
        # Calculate fmax
        fmax_calc = max_len // 2 if max_len > 0 else 250
        
        # GPU processing
        descriptor_settings = {
            'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
            'shape': st.session_state.plot_settings.get('shape', 0.35),
            'kmax': st.session_state.plot_settings.get('kmax', 120.0),
            'int_val': st.session_state.plot_settings.get('int_val', 35.0),
            'b1': st.session_state.plot_settings.get('b1', 5.0),
            'b2': st.session_state.plot_settings.get('b2', 40.0),
            'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
            'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80)
        }
        
        # Call GPU function
        batch_size = st.session_state.plot_settings.get('batch_size', 512)
        descriptors_dict = gpu_funcs['dlogst_spec_descriptor_gpu_2d_chunked'](
            trace_array, 
            st.session_state.dt, 
            fmax=fmax_calc,
            batch_size=batch_size,
            **descriptor_settings
        )
        
        # Unpack results
        num_traces = trace_array.shape[0]
        descriptors_list = []
        
        for i in range(num_traces):
            desc = {}
            for key, value_array in descriptors_dict.items():
                if value_array.shape[0] == num_traces:
                    desc[key] = value_array[i]
            
            # Calculate WOSS if components available
            if all(k in desc for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                desc['WOSS'] = calculate_woss(desc, st.session_state.plot_settings)
            
            descriptors_list.append(desc)
        
        return descriptors_list
        
    except Exception as e:
        st.error(f"GPU processing failed: {e}")
        logging.error(f"GPU batch processing error: {e}", exc_info=True)
        return None

def load_selected_traces():
    """Load trace data for selected indices."""
    if not st.session_state.selected_indices:
        st.error("No traces selected")
        return None
    
    trace_data = []
    segy_path = st.session_state.header_loader.source_file_path
    
    with st.progress(0) as progress_bar:
        for i, idx in enumerate(st.session_state.selected_indices):
            try:
                trace_sample = load_trace_sample(segy_path, idx)
                if trace_sample is not None:
                    trace_data.append({
                        'trace_sample': trace_sample,
                        'trace_idx': idx
                    })
                
                progress_bar.progress((i + 1) / len(st.session_state.selected_indices))
                
            except Exception as e:
                logging.warning(f"Failed to load trace {idx}: {e}")
                continue
    
    st.session_state.loaded_trace_data = trace_data
    return trace_data
```

**Success Criteria:**
- [ ] GPU-only processing enforced
- [ ] Batch processing optimized
- [ ] Error handling robust
- [ ] Memory management efficient

---

## Phase 4: Visualization and Export (Duration: 2-3 days)

### Phase 4.1: Results Visualization Page

**File:** `pages/view_results_page.py`

**Key Features:**
- Interactive plotting with Plotly
- Multiple output types support
- GPU-calculated statistics display
- Export preparation

### Phase 4.2: Export Configuration Page

**File:** `pages/configure_export_page.py`

**Key Features:**
- AOI export configuration
- Attribute selection
- Batch size optimization
- Export format options

### Phase 4.3: Export Processing Page

**File:** `pages/export_process_page.py`

**Key Features:**
- GPU-based batch export
- Real-time progress tracking
- Memory-efficient processing
- Error recovery

### Phase 4.4: Download Page

**File:** `pages/download_export_page.py`

**Key Features:**
- File download interface
- Export summary
- Cleanup utilities

---

## Phase 5: Main Application Router (Duration: 1 day)

### Phase 5.1: Application Entry Point

**File:** `app.py`

**Content:**
```python
import streamlit as st
import logging
from common.constants import APP_TITLE
from common.session_state import initialize_session_state, reset_state
from utils.gpu_utils import initialize_gpu_functions, check_gpu_availability

# Page imports
from pages import (
    load_data_page,
    configure_display_page, 
    select_area_page,
    analyze_data_page,
    view_results_page,
    configure_export_page,
    export_process_page,
    download_export_page
)

# Configure Streamlit
st.set_page_config(
    page_title=APP_TITLE,
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def main():
    """Main application entry point."""
    # Initialize session state
    initialize_session_state()
    
    # Initialize GPU functions
    gpu_funcs = initialize_gpu_functions()
    
    # App title
    st.title(APP_TITLE)
    
    # GPU status
    display_gpu_status()
    
    # Navigation sidebar
    render_navigation()
    
    # Main content area - route to appropriate page
    route_to_page()

def display_gpu_status():
    """Display GPU availability status."""
    if check_gpu_availability():
        st.sidebar.success("🚀 GPU Processing Ready")
    else:
        st.sidebar.error("❌ GPU Required")
        st.error("This application requires GPU processing. Please ensure CUDA is properly installed.")

def render_navigation():
    """Render navigation sidebar."""
    st.sidebar.markdown("---")
    
    # New analysis button
    if st.sidebar.button("🔄 Start New Analysis", use_container_width=True):
        reset_state()
        st.success("New analysis started. All data cleared.")
        st.rerun()
    
    st.sidebar.markdown("---")
    
    # Step navigation (read-only display)
    steps = {
        "load_data": "1. Load Data",
        "configure_display": "2. Configure Parameters", 
        "select_area": "3. Select Area",
        "analyze_data": "4. GPU Processing",
        "view_results": "5. View Results",
        "configure_export": "6A. Configure Export",
        "export_process": "6B. Process Export", 
        "download_export": "6C. Download"
    }
    
    current_step = st.session_state.current_step
    st.sidebar.markdown("**Current Step:**")
    st.sidebar.info(steps.get(current_step, "Unknown"))
    
    # Show available steps
    st.sidebar.markdown("**Workflow Steps:**")
    for step_key, step_name in steps.items():
        if step_key == current_step:
            st.sidebar.markdown(f"👉 **{step_name}**")
        else:
            st.sidebar.markdown(f"   {step_name}")

def route_to_page():
    """Route to the appropriate page based on current step."""
    current_step = st.session_state.current_step
    
    # Enforce GPU requirement for processing steps
    processing_steps = ["analyze_data", "export_process"]
    if current_step in processing_steps and not check_gpu_availability():
        st.error("🚫 GPU processing required for this step")
        st.info("Please ensure CUDA is properly installed and GPU is available.")
        return
    
    # Route to appropriate page
    if current_step == "load_data":
        load_data_page.render()
    elif current_step == "configure_display":
        configure_display_page.render()
    elif current_step == "select_area":
        select_area_page.render() 
    elif current_step == "analyze_data":
        analyze_data_page.render()
    elif current_step == "view_results":
        view_results_page.render()
    elif current_step == "configure_export":
        configure_export_page.render()
    elif current_step == "export_process":
        export_process_page.render()
    elif current_step == "download_export":
        download_export_page.render()
    else:
        st.error(f"Unknown step: {current_step}")
        st.info("Resetting to load data step...")
        st.session_state.current_step = "load_data"
        st.rerun()

if __name__ == "__main__":
    main()
```

**Success Criteria:**
- [ ] Clean routing logic
- [ ] GPU requirement enforced
- [ ] Error handling robust
- [ ] Navigation intuitive

---

## Phase 6: Testing and Optimization (Duration: 2-3 days)

### Phase 6.1: Unit Testing

**Objective:** Create tests for core functionality

**Create:** `tests/` directory with:
- `test_gpu_utils.py`
- `test_data_utils.py` 
- `test_processing.py`
- `test_visualization.py`

### Phase 6.2: Integration Testing

**Objective:** Test complete workflows

**Test Scenarios:**
1. **Inline Processing Workflow:**
   - Load data → Configure → Select inline → GPU process → View results
2. **AOI Export Workflow:**
   - Load data → Configure → Select AOI → Configure export → Process → Download
3. **Well Markers Workflow:**
   - Load data with wells → Configure → Select markers → Process → Results
4. **Polyline Workflow:**
   - Load data → Configure → Import polyline → Process → Results

### Phase 6.3: Performance Optimization

**Objective:** Optimize GPU processing and memory usage

**Optimization Areas:**
1. **GPU Memory Management:**
   - Implement automatic batch size adjustment
   - Add memory monitoring
   - Optimize data transfers

2. **Processing Pipeline:**
   - Pre-allocate arrays where possible
   - Minimize CPU-GPU transfers
   - Implement progressive loading for large datasets

3. **UI Responsiveness:**
   - Add progress indicators
   - Implement background processing where possible
   - Optimize state updates

### Phase 6.4: Error Handling and Recovery

**Objective:** Robust error handling throughout the application

**Error Scenarios:**
- GPU memory exhaustion
- Invalid SEGY files
- Network interruptions during processing
- Incomplete data files
- Export failures

---

## Phase 7: Documentation and Deployment (Duration: 1-2 days)

### Phase 7.1: User Documentation

**Create:**
- `README.md` with installation and usage instructions
- `INSTALLATION.md` with GPU setup requirements
- `USER_GUIDE.md` with workflow examples
- `API_DOCUMENTATION.md` for utility functions

### Phase 7.2: Developer Documentation

**Create:**
- `ARCHITECTURE.md` explaining the modular design
- `CONTRIBUTING.md` for future developers
- `GPU_REQUIREMENTS.md` detailing CUDA dependencies
- Code comments and docstrings throughout

### Phase 7.3: Deployment Preparation

**Actions:**
1. Create `requirements.txt` with all dependencies
2. Create `environment.yml` for Conda environments
3. Add GPU detection and setup scripts
4. Create Docker configuration (optional)
5. Add configuration validation scripts

---

## Implementation Timeline

| Phase | Duration | Key Deliverables | Dependencies |
|-------|----------|------------------|--------------|
| 1 | 1-2 days | Foundation setup, constants, session state | None |
| 2 | 2-3 days | Core utilities migrated | Phase 1 |
| 3 | 3-4 days | All page modules created | Phase 1, 2 |
| 4 | 2-3 days | Visualization and export complete | Phase 3 |
| 5 | 1 day | Main router implemented | Phase 3, 4 |
| 6 | 2-3 days | Testing and optimization | Phase 5 |
| 7 | 1-2 days | Documentation and deployment | Phase 6 |

**Total Estimated Duration: 12-18 days**

---

## Success Metrics

### Technical Metrics
- [ ] GPU processing enforced for all analysis modes
- [ ] Memory usage optimized (< 80% GPU memory)
- [ ] Processing speed improved vs. monolithic version
- [ ] Code coverage > 80% for utility functions
- [ ] Zero critical bugs in core workflows

### User Experience Metrics
- [ ] Workflow completion time reduced
- [ ] Error messages clear and actionable  
- [ ] UI responsive during processing
- [ ] File export success rate > 95%
- [ ] User can complete analysis without technical knowledge

### Maintainability Metrics
- [ ] Code complexity reduced (cyclomatic complexity < 10)
- [ ] Function length < 50 lines average
- [ ] Clear separation of concerns
- [ ] Documentation coverage > 90%
- [ ] New features can be added in < 2 days

---

## Risk Mitigation

### High-Risk Items
1. **GPU Memory Limitations:** Implement adaptive batch sizing and memory monitoring
2. **Large Dataset Processing:** Add progressive loading and streaming capabilities
3. **SEGY File Compatibility:** Extensive testing with various SEGY formats
4. **State Management Complexity:** Comprehensive session state testing

### Contingency Plans
1. **GPU Unavailability:** Clear error messages and installation guidance
2. **Memory Exhaustion:** Graceful degradation with smaller batch sizes
3. **Processing Failures:** Automatic retry with adjusted parameters
4. **Export Failures:** Detailed logging and recovery options

---

## Post-Implementation

### Immediate Next Steps (After Refactoring)
1. **User Acceptance Testing:** Test with real seismic datasets
2. **Performance Benchmarking:** Compare against original implementation
3. **Documentation Review:** Ensure all documentation is accurate
4. **Training Materials:** Create video tutorials for complex workflows

### Future Enhancements
1. **Additional Processing Methods:** Framework ready for new algorithms
2. **Cloud Processing:** Prepare for cloud GPU deployment
3. **Batch Job Support:** Add support for unattended processing
4. **API Development:** Create REST API for programmatic access

---

This detailed implementation guide provides a clear roadmap for transforming the monolithic WOSS application into a modern, modular, GPU-accelerated seismic analysis tool. Each phase builds upon the previous ones, ensuring a systematic and manageable refactoring process while maintaining full functionality and improving performance through GPU-only processing.
