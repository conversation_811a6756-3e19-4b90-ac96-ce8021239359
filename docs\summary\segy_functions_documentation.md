# SEGY File Import and Export Functions Documentation

This document provides an overview of the key functions related to SEGY file import and export in the codebase. The functions are organized into two main sections: SEGY Import Functions and SEGY Export Functions.

## SEGY Import Functions

### 1. SegyIO3D Constructor

**File Path**: `Load/data_classes.py`  
**Line Numbers**: 111-121  
**Description**: Constructor for the SegyIO3D class that handles importing 3D SEGY files. It opens the SEGY file using the segyio library with specified inline and crossline byte positions.  
**Important Parameters**:
- `file_name`: Path to the SEGY file
- `iline_byte`: Byte position for inline (default: 189)
- `xline_byte`: Byte position for crossline (default: 193)

**Code Snippet**:
```python
def __init__(self, file_name, iline_byte=189, xline_byte=193):
    super().__init__()
    self._segyfile = segyio.open(file_name, iline=int(iline_byte), xline=int(xline_byte))

    # get statistics for visualization
    n_slices = 10
    seis = [self.get_iline(random.randint(0, self.get_n_ilines()-1)) for i in range(n_slices)]
    self.vm = np.percentile(seis, 95)
    self.file_name = file_name
    self.iline_byte = iline_byte
    self.xline_byte = xline_byte
```

### 2. SegyIO2D Constructor

**File Path**: `Load/data_classes.py`  
**Line Numbers**: 190-206  
**Description**: Constructor for the SegyIO2D class that handles importing 2D SEGY files. It opens the SEGY file and loads all traces into a NumPy array.  
**Important Parameters**:
- `file_name`: Path to the SEGY file

**Code Snippet**:
```python
def __init__(self, file_name):
    super().__init__()
    seismic_type = "2D"
    try: 
        with segyio.open(file_name, strict=True) as segyfile:
            seismic_type = "3D"
            raise ValueError("You can only use 2D seismic file with this mode")
    except:
        if seismic_type == "3D":
            raise ValueError("You can only use 2D seismic file with this mode")
        with segyio.open(file_name, strict=False) as segyfile:
            self._data = np.stack(list((_.copy() for _ in segyfile.trace[:])))
            self._dt = segyio.tools.dt(segyfile)
            self.vm = np.percentile(self._data, 95)
            self.file_name = file_name
    self.iline_byte = 189
    self.xline_byte = 193
```

### 3. get_segy_header

**File Path**: `Load/0_💾Import_seismic.py`  
**Line Numbers**: 9-11  
**Description**: Retrieves the header information from a SEGY file.  
**Important Parameters**:
- `file_name`: Path to the SEGY file

**Code Snippet**:
```python
def get_segy_header(file_name):
    f = segyio.open(file_name, ignore_geometry = True)
    return segyio.tools.wrap(f.text[0])
```

### 4. get_inline_xline_position

**File Path**: `Load/0_💾Import_seismic.py`  
**Line Numbers**: 13-29  
**Description**: Extracts the inline and crossline positions from a SEGY file.  
**Important Parameters**:
- `file_name`: Path to the SEGY file

**Code Snippet**:
```python
def get_inline_xline_position(file_name):
    f = segyio.open(file_name, ignore_geometry = True)
    _ntraces    = len(f.trace)
    _inlines    = []
    _crosslines = []

    for i in range(_ntraces):
        headeri = f.header[i]
        _inlines.append(headeri[segyio.su.iline])
        _crosslines.append(headeri[segyio.su.xline])

    print(f'{_ntraces} traces')
    print(f'first 10 inlines: {_inlines[:10]}')
    print(f'first 10 crosslines: {_crosslines[:10]}')

    #The loop variable i is only used to look up the the right header, but segyio can manage this for u
    return _inlines, _crosslines
```

### 5. cropped_numpy (SegyIO3D method)

**File Path**: `Load/data_classes.py`  
**Line Numbers**: 158-173  
**Description**: Reads a cropped portion of the 3D seismic data and returns it as a NumPy array.  
**Important Parameters**:
- `min_il`, `max_il`: Minimum and maximum inline indices
- `min_xl`, `max_xl`: Minimum and maximum crossline indices
- `min_z`, `max_z`: Minimum and maximum time/depth slice indices

**Code Snippet**:
```python
def cropped_numpy(self, min_il, max_il, min_xl, max_xl, min_z, max_z):
    """ Reads cropped seismic and returns numpy array

    Args:
        min_il (_type_): min inline
        max_il (_type_): max inline
        min_xl (_type_): min crossline
        max_xl (_type_): max crossline
        min_z (_type_): min timeslice
        max_z (_type_): max timeslice
    """
    assert max_il>min_il, f"max_il must be greater than {min_il}, got: {max_il}"
    assert max_xl>min_xl, f"max_il must be greater than {min_xl}, got: {max_xl}"
    assert max_z>min_z,   f"max_il must be greater than {min_z}, got: {max_z}"

    return np.array([self.get_iline(i)[min_xl:max_xl, min_z:max_z] for i in range(min_il, max_il)])
```

## SEGY Export Functions

### 1. save_data_form

**File Path**: `Load/custom_blocks.py`  
**Line Numbers**: 66-94  
**Description**: Provides a user interface for saving seismic data in different formats (SEGY or NumPy). It calls the appropriate save function based on the selected format and data dimensions.  
**Important Parameters**:
- `session_state`: Streamlit session state
- `seismic`: Seismic data object
- `numpy_data`: NumPy array containing the data to be saved
- `status`: Status message

**Code Snippet**:
```python
def save_data_form(session_state, seismic, numpy_data, status):
    with st.form("save_data"):
        col1, col2, col3 = st.columns(3)
        path = col1.text_input("Path to Folder")
        file_name = col2.text_input("File Name")
        file_format = col3.radio( "What format? ", ('SEGY', 'NUMPY_SUBSET'))
        submitted = st.form_submit_button("Save")
        if submitted:
            with st.spinner('Wait... Exporting the volume'):
                if file_format == "SEGY":
                    if seismic.get_str_format() == "SEGY":
                        if seismic.get_str_dim() == "2D":
                            save_to_segy_2d(seismic, path+file_name, numpy_data, session_state)
                        else:
                            save_to_segy_3d(seismic, path+file_name, numpy_data, session_state)
                        status = "Saving SEGY complete"
                    else:
                        status = "Error: you can not save a SEGY file since the initial seismic was not SEGY."
                else:
                    save_to_numpy(path+file_name, numpy_data)
                    status = "Saving NUMPY complete"
                    # option = st.radio( "Option", ('Save subset', 'Save in original dimensions - It will create the volume in RAM. Are you sure?'))
                    # if st.form_submit_button("Save "):
                    #     if option == 'Save subset':
                    #         status = None
                    #         save_to_numpy(path+file_name, numpy_data)
                    #     else:
                    #         status = "Save in original dimensions"
    return status
```

### 2. save_to_segy_2d, save_to_segy_3d, and save_to_numpy

**Note**: These functions are imported in `Load/custom_blocks.py` (line 4) from the `utils` module, but their implementation is not directly visible in the provided code. Based on their usage in `save_data_form`, we can infer that:

- `save_to_segy_2d`: Saves 2D seismic data to a SEGY file
  - Parameters: `seismic`, `file_path`, `numpy_data`, `session_state`

- `save_to_segy_3d`: Saves 3D seismic data to a SEGY file
  - Parameters: `seismic`, `file_path`, `numpy_data`, `session_state`

- `save_to_numpy`: Saves seismic data to a NumPy file
  - Parameters: `file_path`, `numpy_data`

### 3. crop_and_load_volume

**File Path**: `Load/custom_blocks.py`  
**Line Numbers**: 37-64  
**Description**: Provides a user interface for cropping a seismic volume and loading it into memory. This is often a precursor to exporting the data.  
**Important Parameters**:
- `data`: Seismic data object
- `converted_to_numpy3d`: NumPy 3D array to store the cropped data
- `cropped_info`: Information about the cropping parameters

**Code Snippet**:
```python
def crop_and_load_volume(data, converted_to_numpy3d, cropped_info):
    with st.form("Cropping"):
        col1, col2, col3 = st.columns(3)
        inlines_indx = col1.slider( 'Select a range for Inlines',
        0, data.get_n_ilines()-1, (0, data.get_n_ilines())) 

        xlines_indx = col2.slider( 'Select a range for Xlines',
        0, data.get_n_xlines()-1, (0, data.get_n_xlines())) 
        
        zslice_indx = col3.slider( 'Select a range for Zslice',
        0, data.get_n_zslices()-1, (0, data.get_n_zslices())) 
        
        # Every form must have a submit button.
        submitted = st.form_submit_button("Submit")
        if submitted:
            cropped_info = np.array([[inlines_indx[0], inlines_indx[1]], \
                [xlines_indx[0], xlines_indx[1]], \
                    [zslice_indx[0], zslice_indx[1]]])
            np_data = data.cropped_numpy(inlines_indx[0], inlines_indx[1], \
            xlines_indx[0], xlines_indx[1],\
                zslice_indx[0], zslice_indx[1])
            converted_to_numpy3d = Numpy3D(np_data)
            col1, col2, col3 = st.columns(3)
            col1.info(f"Number of Inlines [{inlines_indx[1]-inlines_indx[0]}]")
            col2.info(f"Number of Xlines [{xlines_indx[1] - xlines_indx[0]}]")
            col3.info(f"Time [{zslice_indx[1]-zslice_indx[0]}]")
            st.success('Volume is loaded')
    return converted_to_numpy3d, cropped_info
```
